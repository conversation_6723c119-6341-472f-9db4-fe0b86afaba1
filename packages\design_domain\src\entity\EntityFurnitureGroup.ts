import { Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree";
import { Vector3 } from "three";
import { ZRect } from "z_polygon";

import { I_DesignMaterialInfo } from "@layoutai/basic_data";

import { MaterialService } from "../service/MaterialService";
import { EntityBase } from "./EntityBase";
import { EntityFurniture } from "./EntityFurniture";
import { EntityType } from "./EntityType";


/**
* @description 组合家具实体
* <AUTHOR>
* @date 2025-06-16
* @lastEditTime 2025-06-16 11:45:41
* @lastEditors xuld
*/
export const EntityFurnitureGroup = types
    .compose(EntityBase, types.model({
        // 是否可见，默认为 true 可见
        visible: types.optional(types.boolean, true),
        // 图元类型
        category: types.string,
        // 子类型
        subCategory: types.string,
        // 素材ID
        materialId: types.string,
        // 位置
        x: types.number,
        y: types.number,
        z: types.number,
        // 法线
        normalX: types.number,
        normalY: types.number,
        normalZ: types.number,
        // 尺寸
        length: types.number,
        width: types.number,
        height: types.number,
        // 子实体
        children: types.optional(types.array(EntityFurniture), []),
    }))
    .named(EntityType.furnitureGroup).props({
        type: EntityType.furnitureGroup
    })
    .actions(self => ({
        // 设置位置
        setX(x: number) {
            self.x = x;
        },
        setY(y: number) {
            self.y = y;
        },
        setZ(z: number) {
            self.z = z;
        },
        setPosition(x: number, y: number, z: number) {
            self.x = x;
            self.y = y;
            self.z = z;
        },
        // 设置法线
        setNormal(normalX: number, normalY: number, normalZ: number) {
            self.normalX = normalX;
            self.normalY = normalY;
            self.normalZ = normalZ;
        },
        // 设置尺寸
        setLength(length: number) {
            self.length = length;
        },
        setWidth(width: number) {
            self.width = width;
        },
        setHeight(height: number) {
            self.height = height;
        },
        setSize(length: number, width: number, height: number) {
            self.length = length;
            self.width = width;
            self.height = height;
        },
        // 设置是否可见
        setVisible(visible: boolean) {
            self.visible = visible;
        },
    }))
    .views(self => ({
        rect() {
            const r = new ZRect(self.length, self.width);
            r.nor = new Vector3(self.normalX, self.normalY, self.normalZ);
            r.rect_center_3d = new Vector3(self.x, self.y, self.z);
            r.updateRect();
            return r;
        },

        async materialInfo(): Promise<I_DesignMaterialInfo | undefined> {
            if (!self.materialId) {
                return;
            }

            let dvoList = await MaterialService.getDesignMaterialInfoByIds([self.materialId]);
            if (!dvoList.length) {
                return;
            }
            return dvoList[0];
        }
    }));

export interface IEntityFurnitureGroup extends Instance<typeof EntityFurnitureGroup> { }
export interface IEntityFurnitureGroupSnapshotIn extends SnapshotIn<typeof EntityFurnitureGroup> { }
export interface IEntityFurnitureGroupSnapshotOut extends SnapshotOut<typeof EntityFurnitureGroup> { }