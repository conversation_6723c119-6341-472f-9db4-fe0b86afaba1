import { IEntityFurniture } from "@layoutai/design_domain";

import { g_figure_alias_dict } from "../draw/FigureImagePaths";
import { TPainter } from "../draw/TPainter";
import { Object2DBase } from "./Object2DBase";


/**
* @description 家具 2D 对象
* <AUTHOR>
* @date 2025-06-27 17:20:59
* @lastEditTime 2025-06-27 17:20:59
* @lastEditors xuld
*/
export class Furniture2D extends Object2DBase {
    // 是否显示文字
    protected _showText: boolean = true;
    // 是否显示轮廓
    protected _spotLight: boolean = false;

    public get furniture(): IEntityFurniture {
        return this.entity as IEntityFurniture;
    }

    public update(): any | undefined {
        if (!this.furniture) {
            console.error("家具实体不存在，无法更新");
            return undefined;
        }

        return this.furniture;
    }

    public render(painter: TPainter): void {

        let rect = this.furniture.rect();

        let font_size: number = 12;
        let line_color: string = "#777777";

        painter.strokeStyle = "#000";

        painter.fillStyle = this._drawParam.fillStyle;

        painter._context.lineWidth = this._drawParam.lineWidth;

        painter._style_mapping["black"] = line_color;

        let category = this.furniture.category;
        let subCategory = this.furniture.subCategory;
        let candidate_labels = [g_figure_alias_dict[category], subCategory, category];
        painter.drawFigureRect(rect, category || subCategory, candidate_labels);

        painter.fillStyle = "#000";
        if (this._showText) {
            painter.fillStyle = this._drawParam.fillStyle;
            let sub_category = subCategory;
            let text = sub_category;
            if (text.length > 5) {
                let ll = text.length;
                text = text.substring(0, ll - 3) + "\n" + text.substring(ll - 3);
            }

            let angle = 0;

            if (rect._w < 200 && rect.w < rect.h) {
                angle = rect.rotation_z + Math.PI / 2;
                font_size /= 2;

            }

            painter.drawText(text, rect.rect_center, angle, font_size);
        }
        if (this._spotLight) {
            painter.drawEdges(rect.edges, 0, "#f00");
        }
    }
} 