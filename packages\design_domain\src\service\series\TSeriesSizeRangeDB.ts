import { Vector3 } from "three";
import { TSize, TSizeRange } from "./TSizeRange";


/**
 *   系列: 单个图元尺寸链数据库
 */
export class TSerialSizeRangeDB {
    static _instance: TSerialSizeRangeDB | null = null;
    private _dict: { [key: string]: { [key: string]: { size_range: TSizeRange, modelIds?: string[] }[] } };

    private last_serial_id: string;
    constructor() {
        this._dict = { "default": {} };
        this.last_serial_id = "default";
    }

    static getInstance() {
        if (!TSerialSizeRangeDB._instance) {
            TSerialSizeRangeDB._instance = new TSerialSizeRangeDB();
        }
        return TSerialSizeRangeDB._instance;
    }
    
    static async LoadSeries(serial_id: string = "default") {
        let dict = TSerialSizeRangeDB.getInstance()._dict;

        if (serial_id === TSerialSizeRangeDB.getInstance().last_serial_id) {
            let data = await fetch("./data").then(val => val.json());
            dict[serial_id] = {};

            for (let key in data) {
                let t_data = data[key];
                dict[serial_id][key] = [];

                for (let range_data of t_data) {
                    let t_range_data = {
                        size_range: new TSizeRange().fromJson(range_data.size_range),
                        modelIds: range_data.modelIds || []
                    };
                    dict[serial_id][key].push(t_range_data);
                }
            }
        }

        let t_dict = dict[serial_id];
        for (let key in t_dict) {
            t_dict[key].sort((a, b) => {
                if (Math.abs(b.size_range.max.x - a.size_range.max.x) < 1) {
                    return b.size_range.max.y - a.size_range.max.y;
                }
                else {
                    return b.size_range.max.x - a.size_range.max.x;
                }
            });
        }

    }
    static GetSizeRangeList(figure_name: string, serial_id: string = "default"): { size_range: TSizeRange, modelIds?: string[] }[] {
        let dict = TSerialSizeRangeDB.getInstance()._dict;
        serial_id = serial_id == "default" ? TSerialSizeRangeDB.getInstance().last_serial_id : serial_id;
        if (!dict[serial_id]) return [];
        return dict[serial_id][figure_name] || [];
    }

    static SetSizeRangeList(size_range_list: { size_range: TSizeRange, modelIds?: string[] }[], figure_name: string, serial_id: string = "default") {
        let dict = TSerialSizeRangeDB.getInstance()._dict;
        serial_id = serial_id == "default" ? TSerialSizeRangeDB.getInstance().last_serial_id : serial_id;
        if (!dict[serial_id]) {
            dict[serial_id] = {}
            return;
        }
        dict[serial_id][figure_name] = size_range_list;
    }

    static QueryDefaultModelIds(figure_name: string, size: TSize, serial_id: string = "default") {
        let list = TSerialSizeRangeDB.GetSizeRangeList(figure_name, serial_id);
        if (!list) return null;

        let modelIds: string[] = [];
        for (let range_data of list) {
            if (range_data.size_range.containsPoint(new Vector3(size.x, size.y, size.z))) {
                modelIds = range_data.modelIds ?? [];
            }
        }
        return modelIds;
    }
}