import { EntityService } from "./EntityService";
import { RoomService } from "./room/RoomService";
import { SchemeSerializeService } from "./scheme/SchemeSerializeService";
import { TSerialSizeRangeDB } from "./series/TSeriesSizeRangeDB";


/**
* @description 服务管理器，用于管理所有服务，内部服务直接相互调用（外部通过 API 中心调用）
* <AUTHOR>
* @date 2025-06-23
* @lastEditTime 2025-06-23 10:30:42
* @lastEditors xuld
*/
export class ServiceManager {
    private static _instance: ServiceManager;
    /**
    * @description 获取该类的单例
    * @return 该类的单例
    */
    public static get instance(): ServiceManager {
        if (!this._instance) {
            this._instance = new ServiceManager();
        }
        return this._instance;
    }

    private _isInit: boolean = false;
    private _entityService: EntityService;
    private _roomService: RoomService;
    private _schemeSerializeService: SchemeSerializeService;

    constructor() {
        this._entityService = new EntityService();
        this._roomService = new RoomService();
        this._schemeSerializeService = new SchemeSerializeService();
    }

    public get entityService(): EntityService {
        return this._entityService;
    }

    public get roomService(): RoomService {
        return this._roomService;
    }

    public get schemeSerializeService(): SchemeSerializeService {
        return this._schemeSerializeService;
    }

    public init(): void {
        if (this._isInit) {
            return;
        }
        this._isInit = true;
        this._entityService.addObserver(this._roomService);
        this._entityService.clearEntity();
        TSerialSizeRangeDB.LoadSeries();
    }
}

(globalThis as any).ServiceManager = ServiceManager;