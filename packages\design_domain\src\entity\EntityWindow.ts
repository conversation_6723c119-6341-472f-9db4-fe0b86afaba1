import { Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree";
import { Vector3 } from "three";
import { ZRect } from "z_polygon";
import { EntityBase } from "./EntityBase";
import { EntityType, RoomEntityRealType } from "./EntityType";
import { I_DesignMaterialInfo, IType2UITypeDict } from "@layoutai/basic_data";
import { MaterialService } from "../service/MaterialService";

/**
 * @description 窗户实体
 */
export const EntityWindow = types
    .compose(
        EntityBase,
        types.model({
            // 是否可见，默认为 true 可见
            visible: types.optional(types.boolean, true),
            // 类型
            realType: types.string,
            // 镜像
            mirror: types.number,
            // 位置
            x: types.number,
            y: types.number,
            // 法线
            normalX: types.number,
            normalY: types.number,
            normalZ: types.number,
            // 旋转
            rotationZ: types.number,
            // 尺寸
            length: types.number,
            width: types.number,
        })
    )
    .named(EntityType.window)
    .props({
        type: EntityType.window,
    })
    .actions((self) => ({
        // 设置位置
        setX(x: number) {
            self.x = x;
        },
        setY(y: number) {
            self.y = y;
        },
        // 设置法线
        setNormal(normalX: number, normalY: number, normalZ: number) {
            self.normalX = normalX;
            self.normalY = normalY;
            self.normalZ = normalZ;
        },
        // 设置尺寸
        setLength(length: number) {
            self.length = length;
        },
        setWidth(width: number) {
            self.width = width;
        },
        setSize(length: number, width: number) {
            self.length = length;
            self.width = width;
        },
        // 设置是否可见
        setVisible(visible: boolean) {
            self.visible = visible;
        },
    }))
    .views((self) => ({
        get z() {
            const zMap: Record<string, number> = {
                [RoomEntityRealType.BayWindow]: 900,
                [RoomEntityRealType.Railing]: 0,
            };
            return zMap[self.realType] ?? 950;
        },
        get height() {
            const heightMap: Record<string, number> = {
                [RoomEntityRealType.BayWindow]: 1600,
                [RoomEntityRealType.Railing]: 2400,
            };
            return heightMap[self.realType] ?? 1250;
        },
        get thickness() {
            if (self.realType === RoomEntityRealType.BayWindow) {
                return Math.max(self.width, 600);
            }
            return self.width;
        },
        get category() {
            return IType2UITypeDict[self.realType];
        },
        get rect() {
            const r = new ZRect(self.length, self.width);
            r.rect_center_3d = new Vector3(self.x, self.y, this.z);
            r.nor = new Vector3(self.normalX, self.normalY, self.normalZ);
            r.updateRect();
            return r;
        },
        get materialId() {
            return MaterialService.getDefaultMaterialId(this.rect, this.category);
        },
        async materialInfo(): Promise<I_DesignMaterialInfo | undefined> {
            if (!this.materialId) {
                return;
            }
            let dvoList = await MaterialService.getDesignMaterialInfoByIds([this.materialId]);
            if (!dvoList.length) {
                return;
            }
            return dvoList[0];
        }
    }));

export interface IEntityWindow extends Instance<typeof EntityWindow> {}
export interface IEntityWindowSnapshotIn extends SnapshotIn<typeof EntityWindow> {}
export interface IEntityWindowSnapshotOut extends SnapshotOut<typeof EntityWindow> {}