import { Instance, SnapshotIn, SnapshotOut, types, IMSTMap } from "mobx-state-tree";

import { IEntityBase } from "../entity/EntityBase";
import { EntityFurniture, IEntityFurniture } from "../entity/EntityFurniture";
import { EntityRoom, IEntityRoom } from "../entity/EntityRoom";
import { EntityWindow, IEntityWindow } from "../entity/EntityWindow";
import { EntityDoor, IEntityDoor } from "../entity/EntityDoor";
import { EntityType } from "../entity/EntityType";
import { EntityWall, IEntityWall } from "../entity/EntityWall";
import { StoreScheme } from "../store/StoreScheme";
import { EntityFurnitureGroup, IEntityFurnitureGroup } from "../entity/EntityFurnitureGroup";


/**
 * @description 实体存储器，用于存储实体，并提供对实体的增删改查操作
 * <AUTHOR>
 * @date 2025-06-16
 * @lastEditTime 2025-06-16 12:03:32
 * @lastEditors xuld
 */
export const EntityStore = types
    .model("EntityStore", {
        scheme: types.optional(StoreScheme, {}),
        wallMap: types.map(EntityWall),
        roomMap: types.map(EntityRoom),
        furnitureMap: types.map(EntityFurniture),
        windowMap: types.map(EntityWindow),
        doorMap: types.map(EntityDoor),
        furnitureGroupMap: types.map(EntityFurnitureGroup),
    })
    .volatile((self) => ({
        _entityMap: new Map<string, IEntityBase>(),
        _typeMap: new Map<EntityType, IMSTMap<any>>([
            [EntityType.wall, self.wallMap],
            [EntityType.room, self.roomMap],
            [EntityType.furniture, self.furnitureMap],
            [EntityType.window, self.windowMap],
            [EntityType.door, self.doorMap],
            [EntityType.furnitureGroup, self.furnitureGroupMap]
        ]),
    }))
    .actions(self => ({
        /**
         * @description 添加实体
         * @param entity 要添加的实体
         * @returns 是否添加成功
         */
        addEntity(entity: IEntityBase): boolean {
            let map = self._typeMap.get(entity.type as EntityType);
            if (!map) {
                console.error("addEntity", entity.type, entity.uuid);
                throw new Error("addEntity error, unknown entity type: " + entity.type);
            }
            if (map.has(entity.uuid)) {
                return false;
            }
            map.set(entity.uuid, entity);
            self._entityMap.set(entity.uuid, entity);

            return true;
        },

        /**
         * @description 删除实体
         * @param uuid 要删除的实体ID
         * @returns 是否删除成功
         */
        removeEntity(uuid: string): boolean {
            if (!uuid) return false;

            let entity = self._entityMap.get(uuid);
            if (!entity) return false;

            let map = self._typeMap.get(entity.type as EntityType);
            if (!map) {
                console.error("removeEntity", entity.type, entity.uuid);
                throw new Error("removeEntity error, unknown entity type: " + entity.type);
            }
            map.delete(uuid);
            self._entityMap.delete(uuid);
            return true;
        },

        /**
         * @description 清空所有实体
         */
        clear(): void {
            self.scheme.reset();
            self._entityMap.clear();
            self._typeMap.forEach(map => map.clear());
        },

        clearEntityByType(type: EntityType): void {
            let map = self._typeMap.get(type);
            if (!map) {
                console.error("clearEntityByType", type);
                throw new Error("clearEntityByType error, unknown entity type: " + type);
            }
            map.clear();
        },
    })).views(self => ({
        /**
         * @description 获取实体
         * @param uuid 实体ID
         * @returns 实体对象,如果不存在返回undefined
         */
        getEntity(uuid: string): IEntityBase | undefined {
            if (!uuid) return undefined;
            let entity = self._entityMap.get(uuid);
            if (!entity) {
                console.warn(`EntityStore.getEntity: 未找到实体: ${uuid}`);
                return undefined;
            }

            let map = self._typeMap.get(entity.type as EntityType);
            if (!map) {
                console.error("getEntity", entity.type, entity.uuid);
                throw new Error("getEntity error, unknown entity type: " + entity.type);
            }
            return map.get(uuid);
        },

        getEntitiesByType(type: EntityType): IEntityBase[] {
            let map = self._typeMap.get(type);
            if (!map) {
                console.error("getEntitiesByType", type);
                throw new Error("getEntitiesByType error, unknown entity type: " + type);
            }
            return Array.from(map.values()) as IEntityBase[];
        },
    }))


export interface IEntityStore extends Instance<typeof EntityStore> { }
export interface IEntityStoreSnapshotIn extends SnapshotIn<typeof EntityStore> { }
export interface IEntityStoreSnapshotOut extends SnapshotOut<typeof EntityStore> { }